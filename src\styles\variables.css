/* 科技风格配色方案 */
:root {
  /* 主色调 - 科技蓝 */
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;

  /* 辅助色 - 深蓝科技色 */
  --secondary-color: #2f54eb;
  --secondary-light: #597ef7;
  --secondary-dark: #1d39c4;

  /* 背景色 */
  --bg-color: #f0f2f5;
  --bg-white: #ffffff;
  --bg-gray: #fafafa;
  --bg-dark: #001529;

  /* 文字颜色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-disabled: #bfbfbf;
  --text-white: #ffffff;

  /* 边框颜色 */
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;

  /* 成功、警告、错误色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;

  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 6px 16px rgba(0, 0, 0, 0.12);

  /* 圆角 */
  --border-radius-small: 4px;
  --border-radius-medium: 6px;
  --border-radius-large: 8px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 布局尺寸 */
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --header-height: 64px;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-primary);
  line-height: 1.5;
}

#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-disabled);
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

/* Element Plus 主题定制 */
:root {
  --el-color-primary: var(--primary-color);
  --el-color-primary-light-3: var(--primary-light);
  --el-color-primary-dark-2: var(--primary-dark);
  --el-border-radius-base: var(--border-radius-medium);
  --el-box-shadow-base: var(--shadow-light);
}

/* 科技风格增强 */
.el-card {
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-large);
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: var(--shadow-medium);
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

.el-table {
  border-radius: var(--border-radius-medium);
  /* 不能使用 hidden，否则 Element Plus 固定列会被裁剪 */
  overflow: visible;
  width: 100%;
}

.el-table th {
  background-color: var(--bg-gray);
  color: var(--text-primary);
  font-weight: 600;
}

/* 表格容器样式 */
.el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保表格在小屏幕上可以水平滚动 */
.table-card .el-card__body {
  overflow-x: auto;
  padding: 0;
}

.table-card .el-table {
  min-width: 800px; /* 设置最小宽度确保表格不会过度压缩 */
}

.el-menu {
  border: none;
}

.el-input__wrapper {
  border-radius: var(--border-radius-medium);
  transition: all 0.3s ease;
}

.el-input__wrapper:hover {
  box-shadow: var(--shadow-light);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 数据可视化风格 */
.chart-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: var(--border-radius-large);
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e6ed" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}
