<template>
  <div class="data-query-container">
    <div class="page-header">
      <h1 class="page-title">数据查询</h1>
      <p class="page-description">通过多种条件组合查询系统中的数据，支持高级筛选和导出功能</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="query-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>查询条件</span>
          <el-button type="text" @click="resetQuery">重置</el-button>
        </div>
      </template>

      <el-form :model="queryForm" label-width="100px" :inline="true">
        <el-form-item label="数据名称">
          <el-input v-model="queryForm.name" placeholder="请输入数据名称" clearable />
        </el-form-item>

        <el-form-item label="数据类别">
          <el-select v-model="queryForm.category" placeholder="请选择类别" clearable>
            <el-option label="环境参数" value="environment" />
            <el-option label="物理参数" value="physics" />
            <el-option label="化学参数" value="chemistry" />
            <el-option label="生物参数" value="biology" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
            <el-option label="有效" :value="1" />
            <el-option label="无效" :value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleAdvancedQuery">
            <el-icon><Setting /></el-icon>
            高级查询
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 查询结果 -->
    <el-card class="result-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>查询结果 (共 {{ total }} 条)</span>
          <div>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
            <el-button @click="handleSaveQuery">
              <el-icon><Star /></el-icon>
              保存查询
            </el-button>
          </div>
        </div>
      </template>

      <div class="table-wrapper">
        <el-table
          :data="resultData"
          style="width: 100%"
          v-loading="loading"
          stripe
          border
          height="400"
        >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="数据名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="category" label="类别" width="120" />
        <el-table-column prop="value" label="数值" min-width="150" />
        <el-table-column prop="unit" label="单位" width="100" />
        <el-table-column prop="source" label="数据源" width="120" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="handleViewDetail(row)">详情</el-button>
            <el-button type="text" @click="handleAnalyze(row)">分析</el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 常用查询 -->
    <el-card class="saved-queries-card" shadow="never">
      <template #header>
        <span>常用查询</span>
      </template>

      <div class="saved-queries">
        <el-tag
          v-for="query in savedQueries"
          :key="query.id"
          class="saved-query-tag"
          @click="loadSavedQuery(query)"
          closable
          @close="deleteSavedQuery(query)"
        >
          {{ query.name }}
        </el-tag>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Setting,
  Download,
  Star
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 查询表单
const queryForm = reactive({
  name: '',
  category: '',
  status: '',
  dateRange: []
})

// 查询结果数据
const resultData = reactive([
  {
    id: 1,
    name: '温度监测数据',
    category: '环境参数',
    value: '25.5°C',
    unit: '摄氏度',
    source: '传感器A1',
    updateTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '湿度监测数据',
    category: '环境参数',
    value: '60%',
    unit: '百分比',
    source: '传感器B2',
    updateTime: '2024-01-15 10:25:00'
  }
])

// 保存的查询
const savedQueries = reactive([
  { id: 1, name: '环境参数查询' },
  { id: 2, name: '物理参数查询' },
  { id: 3, name: '本周数据' }
])

// 处理查询
const handleQuery = () => {
  loading.value = true
  ElMessage.success('查询完成')

  // 模拟API调用
  setTimeout(() => {
    total.value = 50
    loading.value = false
  }, 1000)
}

// 重置查询
const resetQuery = () => {
  Object.assign(queryForm, {
    name: '',
    category: '',
    status: '',
    dateRange: []
  })
  ElMessage.info('查询条件已重置')
}

// 高级查询
const handleAdvancedQuery = () => {
  ElMessage.info('高级查询功能开发中...')
}

// 导出结果
const handleExport = () => {
  ElMessage.info('导出查询结果功能开发中...')
}

// 保存查询
const handleSaveQuery = () => {
  ElMessage.info('保存查询条件功能开发中...')
}

// 查看详情
const handleViewDetail = (row) => {
  ElMessage.info(`查看详情: ${row.name}`)
}

// 分析数据
const handleAnalyze = (row) => {
  ElMessage.info(`分析数据: ${row.name}`)
}

// 加载保存的查询
const loadSavedQuery = (query) => {
  ElMessage.info(`加载查询: ${query.name}`)
}

// 删除保存的查询
const deleteSavedQuery = (query) => {
  const index = savedQueries.findIndex(q => q.id === query.id)
  if (index > -1) {
    savedQueries.splice(index, 1)
    ElMessage.success('删除成功')
  }
}

// 处理页面大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  handleQuery()
}

// 处理当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.data-query-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.query-card,
.result-card,
.saved-queries-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper .el-table {
  min-width: 1100px; /* 合理的最小宽度 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: var(--spacing-lg);
  text-align: right;
}

.saved-queries {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.saved-query-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.saved-query-tag:hover {
  background-color: var(--primary-color);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }

  .card-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }
}
</style>
