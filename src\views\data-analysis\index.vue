<template>
  <div class="data-analysis-container">
    <div class="page-header">
      <h1 class="page-title">数据分析与统计</h1>
      <p class="page-description">对系统数据进行深度分析，生成统计报告和可视化图表</p>
    </div>

    <!-- 分析工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleNewAnalysis">
            <el-icon><Plus /></el-icon>
            新建分析
          </el-button>
          <el-button @click="handleImportData">
            <el-icon><Upload /></el-icon>
            导入数据
          </el-button>
          <el-button @click="handleExportReport">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-select v-model="analysisType" placeholder="选择分析类型" style="width: 200px">
            <el-option label="趋势分析" value="trend" />
            <el-option label="相关性分析" value="correlation" />
            <el-option label="分布分析" value="distribution" />
            <el-option label="异常检测" value="anomaly" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 分析图表区域 -->
    <el-row :gutter="24">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>数据趋势图</span>
              <el-button type="text" @click="handleRefreshChart">刷新</el-button>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon :size="64" class="chart-icon">
                <TrendCharts />
              </el-icon>
              <p>趋势分析图表</p>
              <el-button type="primary" @click="handleGenerateChart">生成图表</el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>数据分布图</span>
              <el-button type="text" @click="handleRefreshChart">刷新</el-button>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon :size="64" class="chart-icon">
                <PieChart />
              </el-icon>
              <p>分布分析图表</p>
              <el-button type="primary" @click="handleGenerateChart">生成图表</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计数据 -->
    <el-card class="stats-card" shadow="never">
      <template #header>
        <span>统计摘要</span>
      </template>

      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6" v-for="stat in statistics" :key="stat.label">
          <div class="stat-item">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-change" :class="stat.trend">
              <el-icon>
                <ArrowUp v-if="stat.trend === 'up'" />
                <ArrowDown v-if="stat.trend === 'down'" />
                <Minus v-if="stat.trend === 'stable'" />
              </el-icon>
              {{ stat.change }}
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 分析报告列表 -->
    <el-card class="reports-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>分析报告</span>
          <el-button type="primary" @click="handleCreateReport">创建报告</el-button>
        </div>
      </template>

      <div class="table-wrapper">
        <el-table :data="reportList" style="width: 100%" stripe>
        <el-table-column prop="name" label="报告名称" min-width="220" show-overflow-tooltip />
        <el-table-column prop="type" label="分析类型" width="120" />
        <el-table-column prop="dataSource" label="数据源" width="150" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="handleViewReport(row)">查看</el-button>
            <el-button type="text" @click="handleDownloadReport(row)">下载</el-button>
            <el-button type="text" @click="handleDeleteReport(row)" class="danger-btn">删除</el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  TrendCharts,
  PieChart,
  ArrowUp,
  ArrowDown,
  Minus
} from '@element-plus/icons-vue'

const analysisType = ref('')

// 统计数据
const statistics = reactive([
  {
    label: '总数据量',
    value: '12,345',
    change: '+5.2%',
    trend: 'up'
  },
  {
    label: '平均值',
    value: '25.8',
    change: '-2.1%',
    trend: 'down'
  },
  {
    label: '标准差',
    value: '3.45',
    change: '0%',
    trend: 'stable'
  },
  {
    label: '异常值',
    value: '23',
    change: '+12.5%',
    trend: 'up'
  }
])

// 报告列表
const reportList = reactive([
  {
    id: 1,
    name: '环境数据月度分析报告',
    type: '趋势分析',
    dataSource: '环境监测系统',
    createTime: '2024-01-15 14:30:00',
    status: 'completed'
  },
  {
    id: 2,
    name: '设备性能相关性分析',
    type: '相关性分析',
    dataSource: '设备监控系统',
    createTime: '2024-01-14 09:15:00',
    status: 'processing'
  }
])

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    completed: 'success',
    processing: 'warning',
    failed: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    processing: '处理中',
    failed: '失败'
  }
  return texts[status] || '未知'
}

// 处理函数
const handleNewAnalysis = () => {
  ElMessage.info('新建分析功能开发中...')
}

const handleImportData = () => {
  ElMessage.info('导入数据功能开发中...')
}

const handleExportReport = () => {
  ElMessage.info('导出报告功能开发中...')
}

const handleGenerateChart = () => {
  ElMessage.success('图表生成中...')
}

const handleRefreshChart = () => {
  ElMessage.info('刷新图表...')
}

const handleCreateReport = () => {
  ElMessage.info('创建报告功能开发中...')
}

const handleViewReport = (row) => {
  ElMessage.info(`查看报告: ${row.name}`)
}

const handleDownloadReport = (row) => {
  ElMessage.info(`下载报告: ${row.name}`)
}

const handleDeleteReport = (row) => {
  ElMessage.info(`删除报告: ${row.name}`)
}
</script>

<style scoped>
.data-analysis-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.toolbar-card,
.stats-card,
.reports-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper .el-table {
  min-width: 1000px; /* 合理的最小宽度 */
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-sm);
}

.chart-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.chart-icon {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-medium);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.stat-change.up {
  color: var(--success-color);
}

.stat-change.down {
  color: var(--error-color);
}

.stat-change.stable {
  color: var(--text-secondary);
}

.danger-btn {
  color: var(--error-color);
}

.danger-btn:hover {
  color: var(--error-color);
  background-color: rgba(255, 77, 79, 0.1);
}
</style>
