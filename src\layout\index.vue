<template>
  <div class="layout-container">
    <!-- 左侧导航栏 -->
    <div class="sidebar" :class="{ collapsed: isCollapsed }">
      <Sidebar :collapsed="isCollapsed" />
    </div>

    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="header">
        <Header @toggle-sidebar="toggleSidebar" :collapsed="isCollapsed" />
      </div>

      <!-- 内容区域 -->
      <div class="content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Sidebar from './components/Sidebar.vue'
import Header from './components/Header.vue'

const isCollapsed = ref(false)

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}
</script>

<style scoped>
.layout-container {
  display: flex;
  height: 100vh;
  background-color: var(--bg-color);
}

.sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-dark);
  transition: width 0.3s ease;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  z-index: 1000;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 保留纵向滚动在 content，主容器不裁剪横向以便固定列正确定位 */
  overflow-x: visible;
  overflow-y: hidden;
}

.header {
  height: var(--header-height);
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
  z-index: 999;
}

.content {
  flex: 1;
  padding: var(--spacing-lg);
  /* 仅纵向滚动，避免出现双横向滚动条 */
  overflow-y: auto;
  overflow-x: hidden;
  background-color: var(--bg-color);
  min-width: 0; /* 确保flex子元素可以收缩 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1001;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .main-container {
    margin-left: 0;
  }

  .content {
    padding: var(--spacing-md);
  }
}
</style>
