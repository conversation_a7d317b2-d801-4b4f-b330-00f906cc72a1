import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表板', icon: 'Dashboard' }
      }
    ]
  },
  {
    path: '/standard-data',
    component: Layout,
    children: [
      {
        path: '',
        name: 'StandardData',
        component: () => import('@/views/standard-data/index.vue'),
        meta: { title: '标准数据管理', icon: 'Document' }
      }
    ]
  },
  {
    path: '/data-query',
    component: Layout,
    children: [
      {
        path: '',
        name: 'DataQuery',
        component: () => import('@/views/data-query/index.vue'),
        meta: { title: '数据查询', icon: 'Search' }
      }
    ]
  },
  {
    path: '/data-analysis',
    component: Layout,
    children: [
      {
        path: '',
        name: 'DataAnalysis',
        component: () => import('@/views/data-analysis/index.vue'),
        meta: { title: '数据分析与统计', icon: 'TrendCharts' }
      }
    ]
  },
  {
    path: '/special-docs',
    component: Layout,
    children: [
      {
        path: '',
        name: 'SpecialDocs',
        component: () => import('@/views/special-docs/index.vue'),
        meta: { title: '特殊文档管理', icon: 'Files' }
      }
    ]
  },
  {
    path: '/user-management',
    component: Layout,
    children: [
      {
        path: '',
        name: 'UserManagement',
        component: () => import('@/views/user-management/index.vue'),
        meta: { title: '用户管理', icon: 'User' }
      }
    ]
  },
  {
    path: '/system-logs',
    component: Layout,
    children: [
      {
        path: '',
        name: 'SystemLogs',
        component: () => import('@/views/system-logs/index.vue'),
        meta: { title: '系统日志', icon: 'List' }
      }
    ]
  },
  {
    path: '/profile',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Profile',
        component: () => import('@/views/profile/index.vue'),
        meta: { title: '个人信息', icon: 'UserFilled' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
