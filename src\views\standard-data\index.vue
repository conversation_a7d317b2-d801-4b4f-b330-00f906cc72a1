<template>
  <div class="standard-data-container">
    <div class="page-header">
      <h1 class="page-title">标准数据管理</h1>
      <p class="page-description">管理和维护系统中的标准数据，确保数据的准确性和一致性</p>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增数据
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键词搜索"
            style="width: 300px"
            clearable
            @change="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-wrapper">
        <el-table
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          stripe
          border
        >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="数据名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="category" label="数据类别" width="120" />
        <el-table-column prop="value" label="数据值" min-width="200" />
        <el-table-column prop="unit" label="单位" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="handleView(row)">查看</el-button>
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(row)" class="danger-btn">删除</el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 表格数据
const tableData = reactive([
  {
    id: 1,
    name: '温度标准值',
    category: '环境参数',
    value: '25.5°C',
    unit: '摄氏度',
    status: 1,
    updateTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '湿度标准值',
    category: '环境参数',
    value: '60%',
    unit: '百分比',
    status: 1,
    updateTime: '2024-01-15 10:25:00'
  },
  {
    id: 3,
    name: '压力标准值',
    category: '物理参数',
    value: '101.325',
    unit: 'kPa',
    status: 0,
    updateTime: '2024-01-14 16:20:00'
  }
])

// 获取状态类型
const getStatusType = (status) => {
  return status === 1 ? 'success' : 'warning'
}

// 获取状态文本
const getStatusText = (status) => {
  return status === 1 ? '有效' : '无效'
}

// 处理新增
const handleAdd = () => {
  ElMessage.info('新增数据功能开发中...')
}

// 处理导入
const handleImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出数据功能开发中...')
}

// 处理搜索
const handleSearch = () => {
  ElMessage.info(`搜索关键词: ${searchKeyword.value}`)
}

// 处理查看
const handleView = (row) => {
  ElMessage.info(`查看数据: ${row.name}`)
}

// 处理编辑
const handleEdit = (row) => {
  ElMessage.info(`编辑数据: ${row.name}`)
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除数据 "${row.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 处理页面大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  loadData()
}

// 处理当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 加载数据
const loadData = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    total.value = 100
    loading.value = false
  }, 500)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.standard-data-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.toolbar-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-sm);
}

.table-card {
  border: 1px solid var(--border-light);
}

.table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper .el-table {
  min-width: 1000px; /* 合理的最小宽度 */
}

.pagination-container {
  margin-top: var(--spacing-lg);
  text-align: right;
}

.danger-btn {
  color: var(--error-color);
}

.danger-btn:hover {
  color: var(--error-color);
  background-color: rgba(255, 77, 79, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .toolbar-left {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .toolbar-right {
    width: 100%;
  }

  .toolbar-right .el-input {
    width: 100% !important;
  }
}
</style>
