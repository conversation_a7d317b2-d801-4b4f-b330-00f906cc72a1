<template>
  <div class="header-container">
    <!-- 左侧区域 -->
    <div class="header-left">
      <el-button
        type="text"
        @click="handleToggleSidebar"
        class="toggle-btn"
      >
        <el-icon :size="20">
          <Fold v-if="!collapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>
      
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item v-if="currentPageTitle">{{ currentPageTitle }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 通知图标 -->
      <el-badge :value="3" class="notification-badge">
        <el-button type="text" class="header-btn">
          <el-icon :size="20">
            <Bell />
          </el-icon>
        </el-button>
      </el-badge>
      
      <!-- 用户信息 -->
      <el-dropdown @command="handleUserCommand" class="user-dropdown">
        <div class="user-info">
          <el-avatar :size="36" :src="userInfo.avatar" class="user-avatar">
            <el-icon><UserFilled /></el-icon>
          </el-avatar>
          <span class="user-name">{{ userInfo.name }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人信息
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Fold,
  Expand,
  Bell,
  UserFilled,
  ArrowDown,
  User,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggle-sidebar'])

const route = useRoute()
const router = useRouter()

// 用户信息
const userInfo = reactive({
  name: '管理员',
  avatar: ''
})

// 当前页面标题
const currentPageTitle = computed(() => {
  const routeMeta = route.meta
  return routeMeta?.title || ''
})

// 切换侧边栏
const handleToggleSidebar = () => {
  emit('toggle-sidebar')
}

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      ElMessage.success('退出登录成功')
      // 这里可以添加退出登录的逻辑
      break
  }
}
</script>

<style scoped>
.header-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  background-color: var(--bg-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-btn {
  margin-right: var(--spacing-md);
  color: var(--text-secondary);
  padding: var(--spacing-sm);
}

.toggle-btn:hover {
  color: var(--primary-color);
  background-color: var(--bg-gray);
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.notification-badge {
  margin-right: var(--spacing-sm);
}

.header-btn {
  color: var(--text-secondary);
  padding: var(--spacing-sm);
}

.header-btn:hover {
  color: var(--primary-color);
  background-color: var(--bg-gray);
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-medium);
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: var(--bg-gray);
}

.user-avatar {
  margin-right: var(--spacing-sm);
}

.user-name {
  font-size: 14px;
  color: var(--text-primary);
  margin-right: var(--spacing-xs);
}

.dropdown-icon {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 var(--spacing-md);
  }
  
  .breadcrumb {
    display: none;
  }
  
  .user-name {
    display: none;
  }
}
</style>
