<template>
  <div class="profile-container">
    <div class="page-header">
      <h1 class="page-title">个人信息</h1>
      <p class="page-description">查看和编辑您的个人资料信息</p>
    </div>

    <el-row :gutter="24">
      <!-- 左侧个人信息卡片 -->
      <el-col :xs="24" :lg="8">
        <el-card class="profile-card" shadow="never">
          <div class="profile-header">
            <div class="avatar-section">
              <el-avatar :size="100" :src="userInfo.avatar" class="user-avatar">
                <el-icon :size="40"><UserFilled /></el-icon>
              </el-avatar>
              <el-button type="text" @click="handleAvatarUpload" class="upload-btn">
                <el-icon><Picture /></el-icon>
                更换头像
              </el-button>
            </div>
            <div class="user-basic-info">
              <h3 class="user-name">{{ userInfo.realName }}</h3>
              <p class="user-title">{{ userInfo.position }}</p>
              <el-tag :type="getRoleType(userInfo.role)">{{ getRoleText(userInfo.role) }}</el-tag>
            </div>
          </div>

          <el-divider />

          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">{{ userInfo.loginCount }}</div>
              <div class="stat-label">登录次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userInfo.dataCount }}</div>
              <div class="stat-label">数据操作</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userInfo.reportCount }}</div>
              <div class="stat-label">生成报告</div>
            </div>
          </div>
        </el-card>

        <!-- 快速操作 -->
        <el-card class="quick-actions-card" shadow="never">
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button @click="handleChangePassword" class="action-btn">
              <el-icon><Lock /></el-icon>
              修改密码
            </el-button>
            <el-button @click="handleSecuritySettings" class="action-btn">
              <el-icon><Setting /></el-icon>
              安全设置
            </el-button>
            <el-button @click="handleNotificationSettings" class="action-btn">
              <el-icon><Bell /></el-icon>
              通知设置
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧详细信息 -->
      <el-col :xs="24" :lg="16">
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-button type="primary" @click="handleEdit" v-if="!isEditing">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <div v-else>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="handleCancel">取消</el-button>
              </div>
            </div>
          </template>

          <el-form
            :model="formData"
            :rules="formRules"
            ref="formRef"
            label-width="100px"
            :disabled="!isEditing"
          >
            <el-row :gutter="24">
              <el-col :xs="24" :sm="12">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="formData.username" disabled />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="formData.realName" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :xs="24" :sm="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="formData.email" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="formData.phone" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :xs="24" :sm="12">
                <el-form-item label="部门">
                  <el-input v-model="formData.department" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="职位">
                  <el-input v-model="formData.position" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="个人简介">
              <el-input
                v-model="formData.bio"
                type="textarea"
                :rows="4"
                placeholder="请输入个人简介..."
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 登录记录 -->
        <el-card class="login-history-card" shadow="never">
          <template #header>
            <span>最近登录记录</span>
          </template>

          <div class="table-wrapper">
            <el-table :data="loginHistory" style="width: 100%" stripe>
            <el-table-column prop="time" label="登录时间" width="180" />
            <el-table-column prop="ip" label="IP地址" width="150" />
            <el-table-column prop="location" label="登录地点" width="150" />
            <el-table-column prop="device" label="设备信息" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'success' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  UserFilled,
  Picture,
  Lock,
  Setting,
  Bell,
  Edit
} from '@element-plus/icons-vue'

const isEditing = ref(false)
const formRef = ref()

// 用户信息
const userInfo = reactive({
  username: 'admin',
  realName: '系统管理员',
  email: '<EMAIL>',
  phone: '13800138000',
  department: '技术部',
  position: '高级工程师',
  role: 'admin',
  bio: '负责系统架构设计和技术管理工作',
  avatar: '',
  loginCount: 1234,
  dataCount: 567,
  reportCount: 89
})

// 表单数据
const formData = reactive({ ...userInfo })

// 表单验证规则
const formRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 登录记录
const loginHistory = reactive([
  {
    time: '2024-01-15 10:30:00',
    ip: '*************',
    location: '北京市',
    device: 'Chrome 120.0.0.0 / Windows 10',
    status: 'success'
  },
  {
    time: '2024-01-14 16:20:00',
    ip: '*************',
    location: '北京市',
    device: 'Chrome 120.0.0.0 / Windows 10',
    status: 'success'
  },
  {
    time: '2024-01-13 09:15:00',
    ip: '*************',
    location: '上海市',
    device: 'Firefox 121.0 / macOS',
    status: 'success'
  }
])

// 获取角色类型
const getRoleType = (role) => {
  const types = {
    admin: 'danger',
    user: 'success',
    guest: 'info'
  }
  return types[role] || 'info'
}

// 获取角色文本
const getRoleText = (role) => {
  const texts = {
    admin: '管理员',
    user: '普通用户',
    guest: '访客'
  }
  return texts[role] || '未知'
}

// 处理头像上传
const handleAvatarUpload = () => {
  ElMessage.info('头像上传功能开发中...')
}

// 处理编辑
const handleEdit = () => {
  isEditing.value = true
}

// 处理保存
const handleSave = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      Object.assign(userInfo, formData)
      isEditing.value = false
      ElMessage.success('保存成功')
    }
  })
}

// 处理取消
const handleCancel = () => {
  Object.assign(formData, userInfo)
  isEditing.value = false
  ElMessage.info('已取消编辑')
}

// 处理修改密码
const handleChangePassword = () => {
  ElMessage.info('修改密码功能开发中...')
}

// 处理安全设置
const handleSecuritySettings = () => {
  ElMessage.info('安全设置功能开发中...')
}

// 处理通知设置
const handleNotificationSettings = () => {
  ElMessage.info('通知设置功能开发中...')
}
</script>

<style scoped>
.profile-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.profile-card,
.quick-actions-card,
.info-card,
.login-history-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.profile-header {
  text-align: center;
}

.avatar-section {
  margin-bottom: var(--spacing-lg);
}

.user-avatar {
  margin-bottom: var(--spacing-sm);
}

.upload-btn {
  display: block;
  margin: 0 auto;
  color: var(--primary-color);
}

.user-basic-info {
  margin-bottom: var(--spacing-md);
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
}

.user-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-sm) 0;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.action-btn {
  justify-content: flex-start;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper .el-table {
  min-width: 1400px; /* 强制表格宽度超过容器，触发水平滚动 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .quick-actions {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .action-btn {
    flex: 1;
    min-width: 120px;
  }
}
</style>
