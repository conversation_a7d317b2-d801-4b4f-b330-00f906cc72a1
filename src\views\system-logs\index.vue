<template>
  <div class="system-logs-container">
    <div class="page-header">
      <h1 class="page-title">系统日志</h1>
      <p class="page-description">查看和管理系统运行日志，监控系统状态和用户操作记录</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" label-width="80px" :inline="true">
        <el-form-item label="日志级别">
          <el-select v-model="filterForm.level" placeholder="选择级别" clearable>
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
            <el-option label="调试" value="debug" />
          </el-select>
        </el-form-item>

        <el-form-item label="操作类型">
          <el-select v-model="filterForm.action" placeholder="选择操作" clearable>
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="数据查询" value="query" />
            <el-option label="数据修改" value="update" />
            <el-option label="系统配置" value="config" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="用户">
          <el-input v-model="filterForm.user" placeholder="用户名" clearable />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleFilter">
            <el-icon><Search /></el-icon>
            筛选
          </el-button>
          <el-button @click="resetFilter">重置</el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志统计 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="24" :sm="12" :lg="6" v-for="stat in logStats" :key="stat.label">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon :size="20">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 日志表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-wrapper">
        <el-table
          :data="logList"
          style="width: 100%"
          v-loading="loading"
          stripe
          border
          height="500"
        >
        <el-table-column prop="timestamp" label="时间" width="180" />
        <el-table-column prop="level" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.level)" size="small">
              {{ getLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="user" label="用户" width="120" />
        <el-table-column prop="action" label="操作" width="120" />
        <el-table-column prop="module" label="模块" width="120" />
        <el-table-column prop="ip" label="IP地址" width="130" />
        <el-table-column prop="message" label="日志内容" min-width="320" show-overflow-tooltip />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="handleViewDetail(row)">详情</el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Download,
  InfoFilled,
  WarningFilled,
  CircleCloseFilled,
  Tools
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 筛选表单
const filterForm = reactive({
  level: '',
  action: '',
  dateRange: [],
  user: ''
})

// 日志统计
const logStats = reactive([
  {
    label: '今日日志',
    value: '2,345',
    icon: 'InfoFilled',
    color: '#1890ff'
  },
  {
    label: '警告数量',
    value: '23',
    icon: 'WarningFilled',
    color: '#faad14'
  },
  {
    label: '错误数量',
    value: '5',
    icon: 'CircleCloseFilled',
    color: '#ff4d4f'
  },
  {
    label: '系统操作',
    value: '156',
    icon: 'Tools',
    color: '#52c41a'
  }
])

// 日志列表
const logList = reactive([
  {
    id: 1,
    timestamp: '2024-01-15 10:30:25',
    level: 'info',
    user: 'admin',
    action: '登录',
    module: '用户管理',
    ip: '*************',
    message: '用户admin登录系统成功'
  },
  {
    id: 2,
    timestamp: '2024-01-15 10:28:15',
    level: 'warning',
    user: 'user001',
    action: '数据查询',
    module: '数据查询',
    ip: '*************',
    message: '查询条件过于宽泛，可能影响性能'
  },
  {
    id: 3,
    timestamp: '2024-01-15 10:25:30',
    level: 'error',
    user: 'system',
    action: '系统配置',
    module: '系统管理',
    ip: '127.0.0.1',
    message: '数据库连接失败，正在尝试重连'
  },
  {
    id: 4,
    timestamp: '2024-01-15 10:20:45',
    level: 'info',
    user: 'user002',
    action: '数据修改',
    module: '标准数据管理',
    ip: '*************',
    message: '更新标准数据记录ID:1234'
  },
  {
    id: 5,
    timestamp: '2024-01-15 10:15:10',
    level: 'debug',
    user: 'system',
    action: '系统监控',
    module: '系统监控',
    ip: '127.0.0.1',
    message: '内存使用率: 65%, CPU使用率: 45%'
  }
])

// 获取级别类型
const getLevelType = (level) => {
  const types = {
    info: 'success',
    warning: 'warning',
    error: 'danger',
    debug: 'info'
  }
  return types[level] || 'info'
}

// 获取级别文本
const getLevelText = (level) => {
  const texts = {
    info: '信息',
    warning: '警告',
    error: '错误',
    debug: '调试'
  }
  return texts[level] || '未知'
}

// 处理筛选
const handleFilter = () => {
  loading.value = true
  ElMessage.success('筛选完成')

  setTimeout(() => {
    total.value = 1000
    loading.value = false
  }, 1000)
}

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    level: '',
    action: '',
    dateRange: [],
    user: ''
  })
  ElMessage.info('筛选条件已重置')
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出日志功能开发中...')
}

// 查看详情
const handleViewDetail = (row) => {
  ElMessage.info(`查看日志详情: ${row.message}`)
}

// 处理页面大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  handleFilter()
}

// 处理当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleFilter()
}

onMounted(() => {
  handleFilter()
})
</script>

<style scoped>
.system-logs-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.filter-card,
.table-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper .el-table {
  min-width: 1200px; /* 合理的最小宽度 */
}

.stats-row {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  border: none;
  border-radius: var(--border-radius-large);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-large);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.pagination-container {
  margin-top: var(--spacing-lg);
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }
}
</style>
