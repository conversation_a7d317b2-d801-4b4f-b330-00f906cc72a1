<template>
  <div class="sidebar-container">
    <!-- Logo区域 -->
    <div class="logo-container">
      <div class="logo">
        <el-icon class="logo-icon" :size="32">
          <DataAnalysis />
        </el-icon>
        <span v-if="!collapsed" class="logo-text">数据管理平台</span>
      </div>
    </div>

    <!-- 导航菜单 -->
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      :collapse="collapsed"
      :unique-opened="true"
      background-color="#001529"
      text-color="#ffffff"
      active-text-color="#1890ff"
      router
    >
      <el-menu-item index="/dashboard">
        <el-icon><Odometer /></el-icon>
        <template #title>仪表板</template>
      </el-menu-item>

      <el-menu-item index="/standard-data">
        <el-icon><Document /></el-icon>
        <template #title>标准数据管理</template>
      </el-menu-item>

      <el-menu-item index="/data-query">
        <el-icon><Search /></el-icon>
        <template #title>数据查询</template>
      </el-menu-item>

      <el-menu-item index="/data-analysis">
        <el-icon><TrendCharts /></el-icon>
        <template #title>数据分析与统计</template>
      </el-menu-item>

      <el-menu-item index="/special-docs">
        <el-icon><Folder /></el-icon>
        <template #title>特殊文档管理</template>
      </el-menu-item>

      <el-menu-item index="/user-management">
        <el-icon><User /></el-icon>
        <template #title>用户管理</template>
      </el-menu-item>

      <el-menu-item index="/system-logs">
        <el-icon><List /></el-icon>
        <template #title>系统日志</template>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  DataAnalysis,
  Odometer,
  Document,
  Search,
  TrendCharts,
  Folder,
  User,
  List
} from '@element-plus/icons-vue'

defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()

const activeMenu = computed(() => {
  return route.path
})
</script>

<style scoped>
.sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-container {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #1f1f1f;
  padding: 0 var(--spacing-md);
}

.logo {
  display: flex;
  align-items: center;
  color: var(--text-white);
  font-size: 18px;
  font-weight: 600;
}

.logo-icon {
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
}

.logo-text {
  white-space: nowrap;
  transition: opacity 0.3s ease;
}

.sidebar-menu {
  flex: 1;
  border: none;
  overflow-y: auto;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: var(--sidebar-width);
}

/* 自定义菜单项样式 */
:deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid #1f1f1f;
  transition: all 0.3s ease;
}

:deep(.el-menu-item:hover) {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #1890ff !important;
  color: #ffffff !important;
  border-right: 3px solid #40a9ff;
}

:deep(.el-menu-item .el-icon) {
  margin-right: var(--spacing-sm);
  font-size: 18px;
}

/* 折叠状态下的样式 */
:deep(.el-menu--collapse .el-menu-item) {
  padding: 0 20px;
}

:deep(.el-menu--collapse .el-menu-item .el-icon) {
  margin-right: 0;
}
</style>
