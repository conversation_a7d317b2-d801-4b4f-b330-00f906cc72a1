<template>
  <div class="user-management-container">
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>
      <p class="page-description">管理系统用户账户、权限分配和用户组织架构</p>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAddUser">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
          <el-button @click="handleBatchImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="handleExportUsers">
            <el-icon><Download /></el-icon>
            导出用户
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-select v-model="filterRole" placeholder="筛选角色" style="width: 150px; margin-right: 16px" clearable>
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
            <el-option label="访客" value="guest" />
          </el-select>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户..."
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </el-card>

    <!-- 用户表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-wrapper">
        <el-table
          :data="userList"
          style="width: 100%"
          v-loading="loading"
          stripe
          border
        >
        <el-table-column type="selection" width="55" />
        <el-table-column width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><UserFilled /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="260" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="lastLogin" label="最后登录" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="handleView(row)">查看</el-button>
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleResetPassword(row)">重置密码</el-button>
            <el-button type="text" @click="handleDelete(row)" class="danger-btn">删除</el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户统计 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="24" :sm="12" :lg="6" v-for="stat in userStats" :key="stat.label">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Search,
  UserFilled,
  User,
  User as Users,
  UserFilled as Avatar
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const filterRole = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 用户列表
const userList = reactive([
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    role: 'admin',
    department: '技术部',
    status: 1,
    lastLogin: '2024-01-15 10:30:00',
    avatar: ''
  },
  {
    id: 2,
    username: 'user001',
    realName: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    role: 'user',
    department: '业务部',
    status: 1,
    lastLogin: '2024-01-14 16:20:00',
    avatar: ''
  },
  {
    id: 3,
    username: 'guest001',
    realName: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    role: 'guest',
    department: '访客',
    status: 0,
    lastLogin: '2024-01-13 09:15:00',
    avatar: ''
  }
])

// 用户统计
const userStats = reactive([
  {
    label: '总用户数',
    value: '156',
    icon: 'Users',
    color: '#1890ff'
  },
  {
    label: '活跃用户',
    value: '128',
    icon: 'User',
    color: '#52c41a'
  },
  {
    label: '管理员',
    value: '8',
    icon: 'Avatar',
    color: '#faad14'
  },
  {
    label: '今日登录',
    value: '45',
    icon: 'UserFilled',
    color: '#f759ab'
  }
])

// 获取角色类型
const getRoleType = (role) => {
  const types = {
    admin: 'danger',
    user: 'success',
    guest: 'info'
  }
  return types[role] || 'info'
}

// 获取角色文本
const getRoleText = (role) => {
  const texts = {
    admin: '管理员',
    user: '普通用户',
    guest: '访客'
  }
  return texts[role] || '未知'
}

// 处理状态变化
const handleStatusChange = (row) => {
  const status = row.status === 1 ? '启用' : '禁用'
  ElMessage.success(`用户 ${row.realName} 已${status}`)
}

// 处理新增用户
const handleAddUser = () => {
  ElMessage.info('新增用户功能开发中...')
}

// 处理批量导入
const handleBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 处理导出用户
const handleExportUsers = () => {
  ElMessage.info('导出用户功能开发中...')
}

// 处理查看
const handleView = (row) => {
  ElMessage.info(`查看用户: ${row.realName}`)
}

// 处理编辑
const handleEdit = (row) => {
  ElMessage.info(`编辑用户: ${row.realName}`)
}

// 处理重置密码
const handleResetPassword = (row) => {
  ElMessageBox.confirm(
    `确定要重置用户 "${row.realName}" 的密码吗？`,
    '确认重置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('密码重置成功')
  }).catch(() => {
    ElMessage.info('已取消重置')
  })
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除用户 "${row.realName}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 处理页面大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  loadData()
}

// 处理当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 加载数据
const loadData = () => {
  loading.value = true
  setTimeout(() => {
    total.value = 156
    loading.value = false
  }, 500)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.user-management-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.toolbar-card,
.table-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper .el-table {
  min-width: 1200px; /* 合理的最小宽度，随浏览器伸缩更自然 */
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-sm);
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: var(--spacing-lg);
  text-align: right;
}

.stats-row {
  margin-top: var(--spacing-lg);
}

.stat-card {
  border: none;
  border-radius: var(--border-radius-large);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-large);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.danger-btn {
  color: var(--error-color);
}

.danger-btn:hover {
  color: var(--error-color);
  background-color: rgba(255, 77, 79, 0.1);
}
</style>
