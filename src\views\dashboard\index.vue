<template>
  <div class="dashboard-container">
    <div class="page-header">
      <h1 class="page-title">数据管理平台仪表板</h1>
      <p class="page-description">欢迎使用科学数据管理平台，这里是您的数据分析中心</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="24" :sm="12" :lg="6" v-for="stat in stats" :key="stat.title">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="24" class="charts-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>数据趋势分析</span>
              <el-button type="text">查看详情</el-button>
            </div>
          </template>
          <div class="chart-placeholder">
            <el-icon :size="48" class="chart-icon">
              <TrendCharts />
            </el-icon>
            <p>数据趋势图表区域</p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>数据分布统计</span>
              <el-button type="text">查看详情</el-button>
            </div>
          </template>
          <div class="chart-placeholder">
            <el-icon :size="48" class="chart-icon">
              <PieChart />
            </el-icon>
            <p>数据分布图表区域</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions-card" shadow="hover">
      <template #header>
        <span>快速操作</span>
      </template>
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="6" v-for="action in quickActions" :key="action.title">
          <div class="quick-action-item" @click="handleQuickAction(action.route)">
            <el-icon :size="32" class="action-icon">
              <component :is="action.icon" />
            </el-icon>
            <div class="action-title">{{ action.title }}</div>
            <div class="action-desc">{{ action.description }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  Document,
  Search,
  TrendCharts,
  Folder,
  User,
  List,
  PieChart
} from '@element-plus/icons-vue'

const router = useRouter()

// 统计数据
const stats = reactive([
  {
    title: '标准数据',
    value: '1,234',
    icon: 'Document',
    color: '#1890ff'
  },
  {
    title: '查询次数',
    value: '5,678',
    icon: 'Search',
    color: '#52c41a'
  },
  {
    title: '分析报告',
    value: '89',
    icon: 'TrendCharts',
    color: '#faad14'
  },
  {
    title: '活跃用户',
    value: '156',
    icon: 'User',
    color: '#f759ab'
  }
])

// 快速操作
const quickActions = reactive([
  {
    title: '数据录入',
    description: '录入新的标准数据',
    icon: 'Document',
    route: '/standard-data'
  },
  {
    title: '数据查询',
    description: '查询和检索数据',
    icon: 'Search',
    route: '/data-query'
  },
  {
    title: '生成报告',
    description: '生成数据分析报告',
    icon: 'TrendCharts',
    route: '/data-analysis'
  },
  {
    title: '文档管理',
    description: '管理特殊文档',
    icon: 'Folder',
    route: '/special-docs'
  }
])

// 处理快速操作点击
const handleQuickAction = (route) => {
  router.push(route)
}
</script>

<style scoped>
.dashboard-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.stats-row {
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  border: none;
  border-radius: var(--border-radius-large);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-large);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-title {
  font-size: 14px;
  color: var(--text-secondary);
}

.charts-row {
  margin-bottom: var(--spacing-xl);
}

.chart-card {
  border: none;
  border-radius: var(--border-radius-large);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.chart-icon {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.quick-actions-card {
  border: none;
  border-radius: var(--border-radius-large);
}

.quick-action-item {
  text-align: center;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-light);
}

.quick-action-item:hover {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.action-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.action-desc {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 24px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }
}
</style>
