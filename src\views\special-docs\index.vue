<template>
  <div class="special-docs-container">
    <div class="page-header">
      <h1 class="page-title">特殊文档管理</h1>
      <p class="page-description">管理系统中的特殊文档，包括技术规范、操作手册、标准文件等</p>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleUpload">
            <el-icon><Upload /></el-icon>
            上传文档
          </el-button>
          <el-button @click="handleBatchUpload">
            <el-icon><FolderAdd /></el-icon>
            批量上传
          </el-button>
          <el-button @click="handleCreateFolder">
            <el-icon><Folder /></el-icon>
            新建文件夹
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文档..."
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </el-card>

    <!-- 文档分类 -->
    <el-row :gutter="24">
      <el-col :xs="24" :md="6">
        <el-card class="category-card" shadow="never">
          <template #header>
            <span>文档分类</span>
          </template>
          <el-tree
            :data="categoryTree"
            :props="treeProps"
            node-key="id"
            :default-expanded-keys="[1]"
            @node-click="handleCategoryClick"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-icon>
                  <Folder v-if="data.type === 'folder'" />
                  <Document v-else />
                </el-icon>
                <span>{{ node.label }}</span>
                <span class="node-count">({{ data.count || 0 }})</span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-col>

      <el-col :xs="24" :md="18">
        <el-card class="docs-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>文档列表</span>
              <div>
                <el-radio-group v-model="viewMode" size="small">
                  <el-radio-button label="list">列表</el-radio-button>
                  <el-radio-button label="grid">网格</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>

          <!-- 列表视图 -->
          <div class="table-wrapper" v-if="viewMode === 'list'">
            <el-table
              :data="documentList"
              style="width: 100%"
              stripe
            >
            <el-table-column width="50">
              <template #default="{ row }">
                <el-icon :size="20" :color="getFileIconColor(row.type)">
                  <component :is="getFileIcon(row.type)" />
                </el-icon>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="文档名称" min-width="220" show-overflow-tooltip />
            <el-table-column prop="type" label="类型" width="100" />
            <el-table-column prop="size" label="大小" width="100" />
            <el-table-column prop="updateTime" label="修改时间" width="180" />
            <el-table-column prop="author" label="上传者" width="120" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="text" @click="handlePreview(row)">预览</el-button>
                <el-button type="text" @click="handleDownload(row)">下载</el-button>
                <el-button type="text" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" @click="handleDelete(row)" class="danger-btn">删除</el-button>
              </template>
            </el-table-column>
            </el-table>
          </div>

          <!-- 网格视图 -->
          <div v-else class="grid-view">
            <div
              v-for="doc in documentList"
              :key="doc.id"
              class="doc-card"
              @click="handlePreview(doc)"
            >
              <div class="doc-icon">
                <el-icon :size="48" :color="getFileIconColor(doc.type)">
                  <component :is="getFileIcon(doc.type)" />
                </el-icon>
              </div>
              <div class="doc-info">
                <div class="doc-name" :title="doc.name">{{ doc.name }}</div>
                <div class="doc-meta">{{ doc.size }} • {{ doc.updateTime }}</div>
              </div>
              <div class="doc-actions">
                <el-button type="text" @click.stop="handleDownload(doc)">
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button type="text" @click.stop="handleEdit(doc)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Upload,
  FolderAdd,
  Folder,
  Search,
  Document,
  Download,
  Edit
} from '@element-plus/icons-vue'

const searchKeyword = ref('')
const viewMode = ref('list')

// 树形结构属性
const treeProps = {
  children: 'children',
  label: 'name'
}

// 分类树数据
const categoryTree = reactive([
  {
    id: 1,
    name: '技术规范',
    type: 'folder',
    count: 15,
    children: [
      { id: 11, name: '数据标准', type: 'folder', count: 8 },
      { id: 12, name: '接口规范', type: 'folder', count: 7 }
    ]
  },
  {
    id: 2,
    name: '操作手册',
    type: 'folder',
    count: 12,
    children: [
      { id: 21, name: '用户手册', type: 'folder', count: 6 },
      { id: 22, name: '管理员手册', type: 'folder', count: 6 }
    ]
  },
  {
    id: 3,
    name: '标准文件',
    type: 'folder',
    count: 20
  }
])

// 文档列表
const documentList = reactive([
  {
    id: 1,
    name: '数据库设计规范.pdf',
    type: 'pdf',
    size: '2.5MB',
    updateTime: '2024-01-15 14:30',
    author: '张三'
  },
  {
    id: 2,
    name: '系统操作手册.docx',
    type: 'docx',
    size: '1.8MB',
    updateTime: '2024-01-14 10:20',
    author: '李四'
  },
  {
    id: 3,
    name: '接口文档.xlsx',
    type: 'xlsx',
    size: '856KB',
    updateTime: '2024-01-13 16:45',
    author: '王五'
  }
])

// 获取文件图标
const getFileIcon = (type) => {
  const icons = {
    pdf: 'Document',
    docx: 'Document',
    xlsx: 'Document',
    txt: 'Document',
    default: 'Document'
  }
  return icons[type] || icons.default
}

// 获取文件图标颜色
const getFileIconColor = (type) => {
  const colors = {
    pdf: '#ff4757',
    docx: '#2f54eb',
    xlsx: '#52c41a',
    txt: '#8c8c8c',
    default: '#8c8c8c'
  }
  return colors[type] || colors.default
}

// 处理分类点击
const handleCategoryClick = (data) => {
  ElMessage.info(`选择分类: ${data.name}`)
}

// 处理上传
const handleUpload = () => {
  ElMessage.info('上传文档功能开发中...')
}

// 处理批量上传
const handleBatchUpload = () => {
  ElMessage.info('批量上传功能开发中...')
}

// 处理创建文件夹
const handleCreateFolder = () => {
  ElMessage.info('创建文件夹功能开发中...')
}

// 处理预览
const handlePreview = (doc) => {
  ElMessage.info(`预览文档: ${doc.name}`)
}

// 处理下载
const handleDownload = (doc) => {
  ElMessage.info(`下载文档: ${doc.name}`)
}

// 处理编辑
const handleEdit = (doc) => {
  ElMessage.info(`编辑文档: ${doc.name}`)
}

// 处理删除
const handleDelete = (doc) => {
  ElMessage.info(`删除文档: ${doc.name}`)
}
</script>

<style scoped>
.special-docs-container {
  padding: 0;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.toolbar-card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-sm);
}

.category-card,
.docs-card {
  border: 1px solid var(--border-light);
}

.table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper .el-table {
  min-width: 1000px; /* 合理的最小宽度 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  width: 100%;
}

.node-count {
  color: var(--text-secondary);
  font-size: 12px;
  margin-left: auto;
}

.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.doc-card {
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.doc-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-medium);
}

.doc-icon {
  text-align: center;
  margin-bottom: var(--spacing-sm);
}

.doc-info {
  text-align: center;
}

.doc-name {
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.doc-meta {
  font-size: 12px;
  color: var(--text-secondary);
}

.doc-actions {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.doc-card:hover .doc-actions {
  opacity: 1;
}

.danger-btn {
  color: var(--error-color);
}

.danger-btn:hover {
  color: var(--error-color);
  background-color: rgba(255, 77, 79, 0.1);
}
</style>
